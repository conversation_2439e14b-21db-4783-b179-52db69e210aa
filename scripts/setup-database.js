require('dotenv').config();
const { sequelize } = require('../src/config/database');
const models = require('../src/models');

async function setupDatabase() {
  try {
    console.log('🔄 Setting up database...');
    
    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    
    // Sync all models (create tables)
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ Database tables synchronized');
    
    console.log('🎉 Database setup completed successfully!');
    
    // Show created tables
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('📋 Created tables:', tables);
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('🔒 Database connection closed');
  }
}

setupDatabase();
